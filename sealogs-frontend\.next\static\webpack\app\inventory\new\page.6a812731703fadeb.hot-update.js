"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/new/page",{

/***/ "(app-pages-browser)/./src/app/ui/inventory/inventory-new.tsx":
/*!************************************************!*\
  !*** ./src/app/ui/inventory/inventory-new.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewInventory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var react_aria_components__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! react-aria-components */ \"(app-pages-browser)/./node_modules/.pnpm/react-aria-components@1.6.0_c73dd79e59f59972f050dd4780f2df6c/node_modules/react-aria-components/dist/Heading.mjs\");\n/* harmony import */ var _app_ui_editor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/ui/editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _components_file_upload__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/file-upload */ \"(app-pages-browser)/./src/components/file-upload.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileText_Package_Paperclip_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileText,Package,Paperclip,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileText_Package_Paperclip_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileText,Package,Paperclip,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileText_Package_Paperclip_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileText,Package,Paperclip,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileText_Package_Paperclip_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileText,Package,Paperclip,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FileText_Package_Paperclip_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FileText,Package,Paperclip,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewInventory(param) {\n    let { vesselID = 0 } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedCategories, setSelectedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [suppliers, setSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedSuppliers, setSelectedSuppliers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [location, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedVessel, setSelectedVessel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [openLocationDialog, setOpenLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openSupplierDialog, setOpenSupplierDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openCategoryDialog, setOpenCategoryDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    var description = \"\";\n    const handleSetVessels = (vessels)=>{\n        const activeVessels = vessels.filter((vessel)=>!vessel.archived);\n        const vesselList = activeVessels.map((item)=>({\n                ...item\n            }));\n        const appendedData = [\n            // { title: '-- Other --', id: 'newLocation' },\n            ...vesselList,\n            {\n                title: \"Other\",\n                id: \"0\"\n            }\n        ];\n        setVessels(appendedData);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getVesselList)(handleSetVessels);\n    const handelSetSuppliers = (data)=>{\n        const suppliersList = [\n            {\n                label: \" ---- Create Supplier ---- \",\n                value: \"newSupplier\"\n            },\n            ...data === null || data === void 0 ? void 0 : data.filter((supplier)=>supplier.name !== null).map((supplier)=>({\n                    label: supplier.name,\n                    value: supplier.id\n                }))\n        ];\n        setSuppliers(suppliersList);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getSupplier)(handelSetSuppliers);\n    const handleSetCategories = (data)=>{\n        const categoriesList = [\n            {\n                label: \" ---- Create Category ---- \",\n                value: \"newCategory\"\n            },\n            ...data === null || data === void 0 ? void 0 : data.filter((category)=>category.name !== null).map((category)=>({\n                    label: category.name,\n                    value: category.id\n                }))\n        ];\n        setCategories(categoriesList);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getInventoryCategory)(handleSetCategories);\n    const handleSetSelectedCategories = (selectedOption)=>{\n        if (selectedOption.find((option)=>option.value === \"newCategory\")) {\n            setOpenCategoryDialog(true);\n        }\n        setSelectedCategories(selectedOption.filter((option)=>option.value !== \"newCategory\"));\n    };\n    const handleEditorChange = (desc)=>{\n        description = desc;\n    };\n    const handleCreate = async ()=>{\n        const variables = {\n            input: {\n                item: document.getElementById(\"inventory-name\").value ? document.getElementById(\"inventory-name\").value : null,\n                description: document.getElementById(\"inventory-short-description\").value ? document.getElementById(\"inventory-short-description\").value : null,\n                content: description,\n                quantity: document.getElementById(\"inventory-qty\").value ? parseInt(document.getElementById(\"inventory-qty\").value) : null,\n                productCode: document.getElementById(\"inventory-code\").value ? document.getElementById(\"inventory-code\").value : null,\n                costingDetails: document.getElementById(\"inventory-cost\").value ? document.getElementById(\"inventory-cost\").value : null,\n                documents: documents.map((doc)=>doc.id).join(\",\"),\n                categories: (selectedCategories === null || selectedCategories === void 0 ? void 0 : selectedCategories.map((category)=>category.value).length) ? selectedCategories.map((category)=>category.value).join(\",\") : null,\n                suppliers: (selectedSuppliers === null || selectedSuppliers === void 0 ? void 0 : selectedSuppliers.map((supplier)=>supplier.value).length) ? selectedSuppliers.map((supplier)=>supplier.value).join(\",\") : null,\n                location: document.getElementById(\"inventory-location\").value ? document.getElementById(\"inventory-location\").value : null,\n                vesselID: vesselID > 0 ? vesselID : selectedLocation === null || selectedLocation === void 0 ? void 0 : selectedLocation.value\n            }\n        };\n        await mutationCreateInventory({\n            variables\n        });\n    };\n    const [mutationCreateInventory, { loading: mutationcreateInventoryLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_INVENTORY, {\n        onCompleted: (response)=>{\n            const data = response.createInventory;\n            if (data.id > 0) {\n                searchParams.get(\"redirect_to\") ? router.push((searchParams === null || searchParams === void 0 ? void 0 : searchParams.get(\"redirect_to\")) + \"\") : router.back();\n            } else {\n                console.error(\"mutationcreateInventory error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationcreateInventory error\", error);\n        }\n    });\n    const handleCreateCategory = async ()=>{\n        const categoryName = document.getElementById(\"inventory-new-category\").value;\n        return await mutationcreateInventoryCategory({\n            variables: {\n                input: {\n                    name: categoryName\n                }\n            }\n        });\n    };\n    const [mutationcreateInventoryCategory, { loading: mutationcreateInventoryCategoryLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_INVENTORY_CATEGORY, {\n        onCompleted: (response)=>{\n            const data = response.createInventoryCategory;\n            if (data.id > 0) {\n                const categoriesList = [\n                    ...categories,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setCategories(categoriesList);\n                const selectedCategoriesList = [\n                    ...selectedCategories,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setSelectedCategories(selectedCategoriesList);\n                setOpenCategoryDialog(false);\n            } else {\n                console.error(\"mutationcreateInventoryCategory error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationcreateInventoryCategory error\", error);\n        }\n    });\n    const handleSelectedVesselChange = (selectedOption)=>{\n        if (selectedOption.value === \"newLocation\") {\n            setOpenLocationDialog(true);\n        }\n        setSelectedLocation(selectedOption);\n    };\n    const handleCreateLocation = (Location)=>{\n        var newLocation = {\n            label: \"\",\n            value: \"\"\n        };\n        if (typeof Location === \"string\") {\n            newLocation = {\n                label: Location,\n                value: Location\n            };\n        }\n        if (typeof Location === \"object\") {\n            newLocation = {\n                label: document.getElementById(\"inventory-new-location\").value,\n                value: document.getElementById(\"inventory-new-location-id\").value ? document.getElementById(\"inventory-new-location-id\").value : document.getElementById(\"inventory-new-location\").value\n            };\n        }\n        const vesselList = vessels.map((item)=>({\n                ...item\n            }));\n        const appendedData = [\n            ...vesselList,\n            {\n                Title: newLocation.label,\n                ID: newLocation.value\n            }\n        ];\n        setVessels(appendedData);\n        setSelectedLocation(newLocation);\n        setOpenLocationDialog(false);\n    };\n    const handleSelectedSuppliers = (selectedOption)=>{\n        if (selectedOption.find((option)=>option.value === \"newSupplier\")) {\n            setOpenSupplierDialog(true);\n        }\n        setSelectedSuppliers(selectedOption.filter((option)=>option.value !== \"newSupplier\"));\n    };\n    const handleCreateSupplier = async ()=>{\n        const name = document.getElementById(\"supplier-name\").value;\n        const website = document.getElementById(\"supplier-website\").value;\n        const phone = document.getElementById(\"supplier-phone\").value;\n        const email = document.getElementById(\"supplier-email\").value;\n        const address = document.getElementById(\"supplier-address\").value;\n        const variables = {\n            input: {\n                name: name,\n                address: address,\n                website: website,\n                email: email,\n                phone: phone\n            }\n        };\n        if (name !== \"\") {\n            await mutationCreateSupplier({\n                variables\n            });\n        }\n        setOpenSupplierDialog(false);\n    };\n    const [mutationCreateSupplier, { loading: mutationcreateSupplierLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_SUPPLIER, {\n        onCompleted: (response)=>{\n            const data = response.createSupplier;\n            if (data.id > 0) {\n                const suppliersList = [\n                    ...suppliers,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setSuppliers(suppliersList);\n                const selectedSuppliersList = [\n                    ...selectedSuppliers,\n                    {\n                        label: data.name,\n                        value: data.id\n                    }\n                ];\n                setSelectedSuppliers(selectedSuppliersList);\n            } else {\n                console.error(\"mutationcreateSupplier error\", response);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"mutationcreateSupplier error\", error);\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_14__.Card, {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_14__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_14__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileText_Package_Paperclip_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_14__.H4, {\n                                    children: \"New Inventory\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        label: \"Inventory Name\",\n                                        htmlFor: \"inventory-name\",\n                                        className: \"text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                            id: \"inventory-name\",\n                                            type: \"text\",\n                                            placeholder: \"Inventory name\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        label: \"Vessel\",\n                                        htmlFor: \"inventory-vessel\",\n                                        className: \"text-sm font-medium\",\n                                        children: vessels ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                                            id: \"inventory-vessel\",\n                                            options: vessels === null || vessels === void 0 ? void 0 : vessels.map((vessel)=>({\n                                                    label: vessel.title,\n                                                    value: vessel.id\n                                                })),\n                                            defaultValues: vesselID > 0 && vessels.filter((vessel)=>vessel.id === vesselID).map((vessel)=>({\n                                                    label: vessel.title,\n                                                    value: vessel.id\n                                                })),\n                                            placeholder: \"Select Vessel \".concat(vesselID),\n                                            onChange: handleSelectedVesselChange\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 33\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_6__.InputSkeleton, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        label: \"Location\",\n                                        htmlFor: \"inventory-location\",\n                                        className: \"text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                            id: \"inventory-location\",\n                                            type: \"text\",\n                                            placeholder: \"Location\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        label: \"Quantity\",\n                                        htmlFor: \"inventory-qty\",\n                                        className: \"text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                            id: \"inventory-qty\",\n                                            type: \"number\",\n                                            placeholder: \"Quantity\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                label: \"Short Description\",\n                                htmlFor: \"inventory-short-description\",\n                                className: \"col-span-2 text-sm font-medium\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                    id: \"inventory-short-description\",\n                                    rows: 12,\n                                    className: \"w-full resize-none\",\n                                    placeholder: \"Short description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium flex items-center gap-2 text-primary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileText_Package_Paperclip_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 29\n                                            }, this),\n                                            \"Inventory Details\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground text-sm leading-relaxed\",\n                                        children: \"In this section categorise the item and add the suppliers where you normally purchase this item and the expected cost. This will help replacing the item in the future.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2 space-y-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        label: \"Product code\",\n                                        htmlFor: \"inventory-code\",\n                                        className: \"grid grid-cols-1 md:grid-cols-[120px_1fr] items-center gap-3 text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                            id: \"inventory-code\",\n                                            type: \"text\",\n                                            placeholder: \"Product code\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        label: \"Categories\",\n                                        htmlFor: \"inventory-categories\",\n                                        className: \"grid grid-cols-1 md:grid-cols-[120px_1fr] items-center gap-3 text-sm font-medium\",\n                                        children: categories ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                                            id: \"inventory-categories\",\n                                            multi: true,\n                                            options: categories,\n                                            value: selectedCategories,\n                                            onChange: handleSetSelectedCategories\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 33\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_6__.InputSkeleton, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        label: \"Supplier\",\n                                        htmlFor: \"inventory-suppliers\",\n                                        className: \"grid grid-cols-1 md:grid-cols-[120px_1fr] items-center gap-3 text-sm font-medium\",\n                                        children: suppliers ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                                            id: \"inventory-suppliers\",\n                                            multi: true,\n                                            value: selectedSuppliers,\n                                            onChange: handleSelectedSuppliers,\n                                            options: suppliers\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 33\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_6__.InputSkeleton, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        label: \"Cost\",\n                                        htmlFor: \"inventory-cost\",\n                                        className: \"grid grid-cols-1 md:grid-cols-[120px_1fr] items-center gap-3 text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                            id: \"inventory-cost\",\n                                            type: \"text\",\n                                            placeholder: \"Costing Details\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 569,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium flex items-center gap-2 text-primary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileText_Package_Paperclip_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 29\n                                            }, this),\n                                            \"Attachment\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground text-sm leading-relaxed\",\n                                        children: \"Upload things like photos of the item, plus warranty and guarantee documents or operating manuals. Add links to any online manuals or product descriptions.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2 space-y-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_file_upload__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            setDocuments: setDocuments,\n                                            text: \"\",\n                                            subText: \"Drag files here or upload\",\n                                            bgClass: \"bg-muted/30 border-2 border-dashed border-muted-foreground/20 rounded-lg p-6\",\n                                            documents: documents\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                        label: \"Links\",\n                                        htmlFor: \"inventory-links\",\n                                        className: \"text-sm font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                            id: \"inventory-links\",\n                                            type: \"text\",\n                                            placeholder: \"Links to manuals or product descriptions\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 585,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 572,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 610,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium flex items-center gap-2 text-primary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FileText_Package_Paperclip_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 29\n                                            }, this),\n                                            \"Description\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground text-sm leading-relaxed\",\n                                        children: \"Enter details that might help with the maintenance or operation of this item.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 614,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_editor__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    id: \"inventory-Content\",\n                                    handleEditorChange: handleEditorChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 613,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 398,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_13__.FooterWrapper, {\n                className: \"mt-5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_FileText_Package_Paperclip_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                        onClick: ()=>router.back(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 635,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                        onClick: handleCreate,\n                        children: \"Create Inventory\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 634,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_14__.AlertDialogNew, {\n                openDialog: openLocationDialog,\n                setOpenDialog: setOpenLocationDialog,\n                handleCreate: ()=>handleCreateLocation({}),\n                actionText: \"Create Location\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_aria_components__WEBPACK_IMPORTED_MODULE_21__.Heading, {\n                        slot: \"title\",\n                        className: \"text-2xl  leading-6 my-2 \",\n                        children: \"Create New Location\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                        label: \"Location\",\n                        htmlFor: \"inventory-new-location\",\n                        className: \"my-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                            id: \"inventory-new-location\",\n                            type: \"text\",\n                            placeholder: \"Location\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                            lineNumber: 657,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 653,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                        label: \"Location ID\",\n                        htmlFor: \"inventory-new-location-id\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                            id: \"inventory-new-location-id\",\n                            type: \"text\",\n                            placeholder: \"Location ID\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                            lineNumber: 664,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 663,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 645,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_14__.AlertDialogNew, {\n                openDialog: openSupplierDialog,\n                setOpenDialog: setOpenSupplierDialog,\n                handleCreate: handleCreateSupplier,\n                actionText: \"Create Supplier\",\n                className: \"lg:max-w-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_aria_components__WEBPACK_IMPORTED_MODULE_21__.Heading, {\n                        slot: \"title\",\n                        className: \"text-2xl  leading-6 my-2 \",\n                        children: \"Create New Supplier\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                    id: \"supplier-name\",\n                                    type: \"text\",\n                                    placeholder: \"Supplier name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 683,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                    id: \"supplier-website\",\n                                    type: \"text\",\n                                    placeholder: \"Website\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 689,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                    id: \"supplier-phone\",\n                                    type: \"text\",\n                                    placeholder: \"Phone\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                    id: \"supplier-email\",\n                                    type: \"email\",\n                                    placeholder: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 703,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                    id: \"supplier-address\",\n                                    rows: 4,\n                                    className: \" p-2\",\n                                    placeholder: \"Supplier address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                                lineNumber: 710,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 681,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 672,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_14__.AlertDialogNew, {\n                openDialog: openCategoryDialog,\n                setOpenDialog: setOpenCategoryDialog,\n                handleCreate: handleCreateCategory,\n                actionText: \"Create Category\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_aria_components__WEBPACK_IMPORTED_MODULE_21__.Heading, {\n                        slot: \"title\",\n                        className: \"text-2xl  leading-6 my-2 \",\n                        children: \"Create New Category\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 726,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                            id: \"inventory-new-category\",\n                            type: \"text\",\n                            placeholder: \"Category\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                        lineNumber: 729,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\inventory\\\\inventory-new.tsx\",\n                lineNumber: 721,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NewInventory, \"oNVcfeakna0lpaiWO9Usb234muo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useMutation\n    ];\n});\n_c = NewInventory;\nvar _c;\n$RefreshReg$(_c, \"NewInventory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/inventory/inventory-new.tsx\n"));

/***/ })

});