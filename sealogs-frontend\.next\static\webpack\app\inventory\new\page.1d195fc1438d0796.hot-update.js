"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/new/page",{

/***/ "(app-pages-browser)/./src/components/file-upload.tsx":
/*!****************************************!*\
  !*** ./src/components/file-upload.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FileUpload; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var react_aria_components__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-aria-components */ \"(app-pages-browser)/./node_modules/.pnpm/react-aria-components@1.6.0_c73dd79e59f59972f050dd4780f2df6c/node_modules/react-aria-components/dist/Heading.mjs\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction FileUpload(param) {\n    let { setDocuments, text = \"Documents and Images\", subText, bgClass = \"\", documents, multipleUpload = true } = param;\n    _s();\n    /* ------------------------------------------------------- */ /* state / refs                                            */ /* ------------------------------------------------------- */ const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentFiles, setCurrentFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openFileNameDialog, setOpenFileNameDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoader, setImageLoader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /* ------------------------------------------------------- */ /* helpers                                                 */ /* ------------------------------------------------------- */ const dropZoneClasses = (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none\", dragActive ? \"bg-slorange-400/60 border-slorange-600\" : \"bg-slorange-300/40 border-slorange-1000\", \"text-slorange-1000 hover:bg-slorange-400/40\", \"min-h-[10rem] cursor-pointer select-none\", bgClass);\n    const uploadFile = async (file)=>{\n        const formData = new FormData();\n        formData.append(\"FileData\", file, file.name.replace(/\\s/g, \"\"));\n        try {\n            const response = await fetch(\"\".concat(\"https://api.sealogs.com/api/\", \"v2/upload\"), {\n                method: \"POST\",\n                headers: {\n                    Authorization: \"Bearer \".concat(localStorage.getItem(\"sl-jwt\"))\n                },\n                body: formData\n            });\n            const data = await response.json();\n            await getFileDetails({\n                variables: {\n                    id: [\n                        data[0].id\n                    ]\n                }\n            });\n            setImageLoader(false);\n        } catch (err) {\n            /* eslint-disable-next-line no-console */ console.error(err);\n        }\n    };\n    /* ------------------------------------------------------- */ /* apollo hooks                                            */ /* ------------------------------------------------------- */ const [getFileDetails] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_8__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_3__.GET_FILES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            setCurrentFiles((prev)=>[\n                    ...prev,\n                    response.readFiles.nodes[0]\n                ]);\n            setOpenFileNameDialog(true);\n        },\n        onError: (error)=>console.error(error)\n    });\n    const [updateFile] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_9__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UPDATE_FILE, {\n        onCompleted: (response)=>{\n            const updated = response.updateFile;\n            setFiles([]);\n            setDocuments((prev)=>multipleUpload ? [\n                    ...prev,\n                    updated\n                ] : [\n                    updated\n                ]);\n        },\n        onError: (error)=>console.error(error)\n    });\n    /* ------------------------------------------------------- */ /* event handlers                                          */ /* ------------------------------------------------------- */ const handleFiles = (fileList)=>{\n        const arr = Array.from(fileList);\n        setFiles((prev)=>[\n                ...prev,\n                ...arr\n            ]);\n        setImageLoader(true);\n        arr.forEach(uploadFile);\n    };\n    const onChange = (e)=>{\n        if (e.target.files) handleFiles(e.target.files);\n    };\n    const onDrop = (e)=>{\n        e.preventDefault();\n        setDragActive(false);\n        if (e.dataTransfer.files) handleFiles(e.dataTransfer.files);\n    };\n    const onDragToggle = (state)=>(e)=>{\n            e.preventDefault();\n            setDragActive(state);\n        };\n    const handleUpdateFileName = ()=>{\n        currentFiles.forEach((file, index)=>{\n            const newFileName = document.getElementById(\"file-name-\".concat(index)).value;\n            updateFile({\n                variables: {\n                    input: {\n                        id: file.id,\n                        title: newFileName\n                    }\n                }\n            });\n        });\n        setOpenFileNameDialog(false);\n    };\n    const openFileExplorer = ()=>{\n        var _inputRef_current;\n        return (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.click();\n    };\n    /* ------------------------------------------------------- */ /* effects                                                 */ /* ------------------------------------------------------- */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (documents === null || documents === void 0 ? void 0 : documents.length) setFiles((prev)=>[\n                ...prev,\n                ...documents\n            ]);\n    }, [\n        documents\n    ]);\n    /* ------------------------------------------------------- */ /* render                                                  */ /* ------------------------------------------------------- */ return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full pt-4 lg:pt-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: dropZoneClasses,\n                onSubmit: (e)=>e.preventDefault(),\n                onDragEnter: onDragToggle(true),\n                onDragOver: onDragToggle(true),\n                onDragLeave: onDragToggle(false),\n                onDrop: onDrop,\n                \"aria-label\": \"File uploader drop zone\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute top-4 left-4 text-xs font-medium uppercase tracking-wider\",\n                        children: text\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                        ref: inputRef,\n                        type: \"file\",\n                        className: \"hidden\",\n                        multiple: multipleUpload,\n                        accept: \".xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf\",\n                        onChange: onChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: openFileExplorer,\n                        className: \"flex flex-col items-center gap-2 focus:outline-none\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: \"/sealogs-document_upload.svg\",\n                                alt: \"Upload illustration\",\n                                width: 96,\n                                height: 96,\n                                priority: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 21\n                            }, this),\n                            subText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-slate-600\",\n                                children: subText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                lineNumber: 153,\n                columnNumber: 13\n            }, this),\n            imageLoader ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 flex justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"h-6 w-6 animate-spin text-slorange-600\",\n                    viewBox: \"0 0 24 24\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            className: \"opacity-25\",\n                            cx: \"12\",\n                            cy: \"12\",\n                            r: \"10\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"4\",\n                            fill: \"none\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            className: \"opacity-75\",\n                            fill: \"currentColor\",\n                            d: \"M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                lineNumber: 198,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.AlertDialogNew, {\n                openDialog: openFileNameDialog,\n                setOpenDialog: setOpenFileNameDialog,\n                handleCreate: handleUpdateFileName,\n                actionText: \"Save\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_aria_components__WEBPACK_IMPORTED_MODULE_10__.Heading, {\n                        slot: \"title\",\n                        className: \"mb-4 text-2xl font-semibold\",\n                        children: \"File Name\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        slot: \"content\",\n                        className: \"space-y-3\",\n                        children: currentFiles.map((file, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                id: \"file-name-\".concat(idx),\n                                defaultValue: file.title,\n                                placeholder: \"File name\",\n                                className: \"w-full rounded-lg border bg-white px-3 py-2 shadow-sm focus-visible:ring-2 focus-visible:ring-slorange-500\"\n                            }, file.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 29\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n                lineNumber: 219,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\file-upload.tsx\",\n        lineNumber: 152,\n        columnNumber: 9\n    }, this);\n}\n_s(FileUpload, \"Y5g+It1arbNHiLfFc+8IxRObH50=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_8__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_9__.useMutation\n    ];\n});\n_c = FileUpload;\nvar _c;\n$RefreshReg$(_c, \"FileUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/file-upload.tsx\n"));

/***/ })

});