'use client'

import { useEffect, useRef, useState, DragE<PERSON>, ChangeEvent } from 'react'
import Image from 'next/image'
import { useLazyQuery, useMutation } from '@apollo/client'
import { Heading } from 'react-aria-components'

import { GET_FILES } from '@/app/lib/graphQL/query'
import { UPDATE_FILE } from '@/app/lib/graphQL/mutation'
import { cn } from '@/app/lib/utils'
import { Input } from '@/components/ui/input'
import { AlertDialogNew } from '@/components/ui'

type FileUploadProps = {
    setDocuments: (files: any[]) => void
    text?: string
    subText?: string
    bgClass?: string
    documents: Array<Record<string, any>>
    multipleUpload?: boolean
}

export default function FileUpload({
    setDocuments,
    text = 'Documents and Images',
    subText,
    bgClass = '',
    documents,
    multipleUpload = true,
}: FileUploadProps) {
    /* ------------------------------------------------------- */
    /* state / refs                                            */
    /* ------------------------------------------------------- */
    const [dragActive, setDragActive] = useState(false)
    const [files, setFiles] = useState<Array<Record<string, any>>>([])
    const [currentFiles, setCurrentFiles] = useState<any[]>([])
    const [openFileNameDialog, setOpenFileNameDialog] = useState(false)
    const [imageLoader, setImageLoader] = useState(false)
    const inputRef = useRef<HTMLInputElement>(null)

    /* ------------------------------------------------------- */
    /* helpers                                                 */
    /* ------------------------------------------------------- */
    const dropZoneClasses = cn(
        'relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none',
        dragActive
            ? 'bg-slorange-400/60 border-slorange-600'
            : 'bg-slorange-300/40 border-slorange-1000',
        'text-slorange-1000 hover:bg-slorange-400/40',
        'min-h-[10rem] cursor-pointer select-none',
        bgClass,
    )

    const uploadFile = async (file: File) => {
        const formData = new FormData()
        formData.append('FileData', file, file.name.replace(/\s/g, ''))
        try {
            const response = await fetch(
                `${process.env.API_BASE_URL}v2/upload`,
                {
                    method: 'POST',
                    headers: {
                        Authorization: `Bearer ${localStorage.getItem('sl-jwt')}`,
                    },
                    body: formData,
                },
            )
            const data = await response.json()
            await getFileDetails({ variables: { id: [data[0].id] } })
            setImageLoader(false)
        } catch (err) {
            /* eslint-disable-next-line no-console */
            console.error(err)
        }
    }

    /* ------------------------------------------------------- */
    /* apollo hooks                                            */
    /* ------------------------------------------------------- */
    const [getFileDetails] = useLazyQuery(GET_FILES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            setCurrentFiles((prev) => [...prev, response.readFiles.nodes[0]])
            setOpenFileNameDialog(true)
        },
        onError: (error) => console.error(error),
    })

    const [updateFile] = useMutation(UPDATE_FILE, {
        onCompleted: (response) => {
            const updated = response.updateFile
            setFiles([])
            setDocuments((prev) =>
                multipleUpload ? [...prev, updated] : [updated],
            )
        },
        onError: (error) => console.error(error),
    })

    /* ------------------------------------------------------- */
    /* event handlers                                          */
    /* ------------------------------------------------------- */
    const handleFiles = (fileList: FileList) => {
        const arr = Array.from(fileList)
        setFiles((prev) => [...prev, ...arr])
        setImageLoader(true)
        arr.forEach(uploadFile)
    }

    const onChange = (e: ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) handleFiles(e.target.files)
    }

    const onDrop = (e: DragEvent<HTMLFormElement>) => {
        e.preventDefault()
        setDragActive(false)
        if (e.dataTransfer.files) handleFiles(e.dataTransfer.files)
    }

    const onDragToggle = (state: boolean) => (e: DragEvent) => {
        e.preventDefault()
        setDragActive(state)
    }

    const handleUpdateFileName = () => {
        currentFiles.forEach((file, index) => {
            const newFileName = (
                document.getElementById(
                    `file-name-${index}`,
                ) as HTMLInputElement
            ).value
            updateFile({
                variables: { input: { id: file.id, title: newFileName } },
            })
        })
        setOpenFileNameDialog(false)
    }

    const openFileExplorer = () => inputRef.current?.click()

    /* ------------------------------------------------------- */
    /* effects                                                 */
    /* ------------------------------------------------------- */
    useEffect(() => {
        if (documents?.length) setFiles((prev) => [...prev, ...documents])
    }, [documents])

    /* ------------------------------------------------------- */
    /* render                                                  */
    /* ------------------------------------------------------- */
    return (
        <div className="w-full pt-4 lg:pt-0">
            <form
                className={dropZoneClasses}
                onSubmit={(e) => e.preventDefault()}
                onDragEnter={onDragToggle(true)}
                onDragOver={onDragToggle(true)}
                onDragLeave={onDragToggle(false)}
                onDrop={onDrop}
                aria-label="File uploader drop zone">
                {/* heading */}
                <span className="absolute top-4 left-4 text-xs font-medium uppercase tracking-wider">
                    {text}
                </span>

                {/* hidden native input */}
                <Input
                    ref={inputRef}
                    type="file"
                    className="hidden"
                    multiple={multipleUpload}
                    accept=".xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf"
                    onChange={onChange}
                />

                {/* interactive area */}
                <button
                    type="button"
                    onClick={openFileExplorer}
                    className="flex flex-col items-center gap-2 focus:outline-none">
                    <Image
                        src="/sealogs-document_upload.svg"
                        alt="Upload illustration"
                        width={96}
                        height={96}
                        priority
                    />
                    {subText && (
                        <span className="text-sm font-medium text-slate-600">
                            {subText}
                        </span>
                    )}
                </button>
            </form>

            {/* loader & filename dialog */}
            {imageLoader ? (
                <div className="mt-4 flex justify-center">
                    <svg
                        className="h-6 w-6 animate-spin text-slorange-600"
                        viewBox="0 0 24 24">
                        <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                            fill="none"
                        />
                        <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                        />
                    </svg>
                </div>
            ) : (
                <AlertDialogNew
                    openDialog={openFileNameDialog}
                    setOpenDialog={setOpenFileNameDialog}
                    handleCreate={handleUpdateFileName}
                    actionText="Save">
                    <Heading
                        slot="title"
                        className="mb-4 text-2xl font-semibold">
                        File Name
                    </Heading>
                    <div slot="content" className="space-y-3">
                        {currentFiles.map((file, idx) => (
                            <Input
                                key={file.id}
                                id={`file-name-${idx}`}
                                defaultValue={file.title}
                                placeholder="File name"
                                className="w-full rounded-lg border bg-white px-3 py-2 shadow-sm focus-visible:ring-2 focus-visible:ring-slorange-500"
                            />
                        ))}
                    </div>
                </AlertDialogNew>
            )}
        </div>
    )
}
